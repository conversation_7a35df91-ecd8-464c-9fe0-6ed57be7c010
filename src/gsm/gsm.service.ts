import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Interval } from '@nestjs/schedule';
import { PortInfo, list as SerialPortList } from 'serialport';
import GsmEntity, { GsmEventType } from './entities/gsm.entity';
import { ClientKafka } from '@nestjs/microservices';
import { CreateMessageDto } from '@sms-receiver/dto';

@Injectable()
export class GsmService implements OnModuleInit {
  private readonly logger = new Logger(GsmService.name);
  private gsmModemMap = new Map<string, GsmEntity>();
  constructor(
    @Inject('SMS_SERVICE') private readonly smsService: ClientKafka,
  ) {}

  initGsmModem(deviceKey: string, devicePath: string) {
    const gsmModem = new GsmEntity(devicePath);
    this.gsmModemMap.set(deviceKey, gsmModem);
    gsmModem.on(GsmEventType.NEW_MESSAGE, this.onNewMessage(deviceKey));
    gsmModem.init();
    gsmModem.open();
    return gsmModem;
  }

  static deviceHash(portInfo: PortInfo): string {
    return `${portInfo.productId}_${portInfo.vendorId}_${
      portInfo.locationId || portInfo.path
    }`;
  }

  onNewMessage = (deviceKey: string) => {
    return async (messageDetails: any) => {
      await Promise.all(
        messageDetails.map(async (messageDetail) => {
          const { sender, message, dateTimeSent } = messageDetail;
          console.log({
            messageDetail: JSON.stringify(messageDetail),
            deviceKey,
            content: message,
            receivedAt: dateTimeSent,
            sender,
          });
          try {
            const r = await this.smsService
              .send<unknown, CreateMessageDto>(CreateMessageDto.name, {
                ...(this.gsmModemMap.get(deviceKey).simId !== undefined
                  ? { simId: this.gsmModemMap.get(deviceKey).simId }
                  : { deviceKey }),
                content: message,
                receivedAt: dateTimeSent,
                sender,
              })
              .toPromise();
            console.log(r);
          } catch (e) {
            console.log(e);
          }
        }),
      );
    };
  };

  @Interval(30000)
  async handleScanDevices() {
    const list = await SerialPortList();
    const gsmDeviceList = list.filter((portInfo) => {
      return (
        (portInfo.vendorId === '1a86' && portInfo.productId === '7523') ||
        (portInfo.vendorId === '2c7c' &&
          portInfo.productId === '0125' &&
          portInfo.pnpId === 'usb-Quectel_EG25-G-if02-port0')
      );
    });
    const deviceKeys: string[] = [];
    gsmDeviceList.forEach((portInfo) => {
      const deviceKey = GsmService.deviceHash(portInfo);
      if (!this.gsmModemMap.has(deviceKey)) {
        this.logger.log(`New Device Attached: ${deviceKey}`);
        console.log(portInfo);
        this.initGsmModem(deviceKey, portInfo.path);
      }
      deviceKeys.push(deviceKey);
    });
    for (const deviceKey of this.gsmModemMap.keys()) {
      if (deviceKeys.indexOf(deviceKey) === -1) {
        this.logger.log(`Device Detached: ${deviceKey}`);
        this.gsmModemMap.delete(deviceKey);
      }
    }
  }

  async onModuleInit() {
    this.smsService.subscribeToResponseOf(CreateMessageDto.name);
    await this.smsService.connect();
    await this.handleScanDevices();
  }
}
