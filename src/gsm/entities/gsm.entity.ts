import { EventEmitter } from 'events';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { Modem } from 'serialport-gsm';

export enum GsmEventType {
  OPEN = 'open',
  CLOSE = 'close',
  ERROR = 'error',
  NEW_MESSAGE = 'new_message',
  SIM_ID = 'sim_id',
}

export default class GsmEntity extends EventEmitter {
  modem = Modem();
  options = {};
  simId?: string;
  constructor(private readonly serialPort: string, options = {}) {
    super();
    this.serialPort = serialPort;
    Object.assign(this.options, this.defaultOptions, options);
  }

  init() {
    this.modem.on('open', this.onOpen);
    this.modem.on('close', this.onClose);
    this.modem.on('error', this.onError);
    this.modem.on('onNewMessage', this.onNewMessage);
  }

  get defaultOptions() {
    return {
      baudRate: 115200,
      dataBits: 8,
      stopBits: 1,
      parity: 'none',
      rtscts: false,
      xon: false,
      xoff: false,
      xany: false,
      autoDeleteOnReceive: true,
      enableConcatenation: true,
      incomingCallIndication: false,
      incomingSMSIndication: true,
      pin: '',
      customInitCommand: '',
      logger: {
        debug: (...args: any) => {
          console.debug(this.serialPort, args);
        },
      },
    };
  }

  open() {
    this.modem.open(this.serialPort, this.options);
  }

  onOpen = async (...args: any[]) => {
    try {
      await this.modem.initializeModem(null, false, 300000);
      // this.modem.getOwnNumber(console.log);
      this.modem.addListener({
        match(newPart) {
          return (
            newPart.startsWith('89') &&
            (newPart.length === 19 || newPart.length === 20)
          );
        },
        process: (newPart) => {
          this.simId = newPart.substr(0, newPart.length - 1);
          console.log('sim Id', this.simId);
          this.emit(GsmEventType.SIM_ID, this.simId);
        },
      });
      this.modem.executeCommand('AT+CCID', (result) => {
        console.log(this.serialPort, result);
        if (
          result.status === 'success' &&
          result.data.result.trim().startsWith('89')
        ) {
          this.simId = result.data.result
            .trim()
            .substr(0, result.data.result.trim().length - 1);
          console.log('sim Id', this.simId);
          this.emit(GsmEventType.SIM_ID, this.simId);
        }
      });
    } catch (e) {
      console.error(this.serialPort, e);
    }

    this.emit(GsmEventType.OPEN, args);
  };

  onClose = async (...args: any[]) => {
    this.emit(GsmEventType.CLOSE, args);
  };

  onError = async (...args: any[]) => {
    setTimeout(() => {
      this.open();
    }, 5000);
    this.emit(GsmEventType.ERROR, args);
  };

  onNewMessage = (messageDetails: any) => {
    this.emit(GsmEventType.NEW_MESSAGE, messageDetails);
  };
}
