import { Modu<PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GsmController } from './gsm.controller';
import { GsmService } from './gsm.service';
import { CustomClientProxyFactory } from '@sms-receiver/common';
import { ClientProxyFactory } from '@nestjs/microservices';

@Module({
  controllers: [GsmController],
  providers: [
    ConfigService,
    {
      provide: 'SMS_SERVICE',
      useFactory: (configService: ConfigService) => {
        const smsServiceOptions = configService.get('smsService');
        return ClientProxyFactory.create({
          customClass: CustomClientProxyFactory,
          options: {
            client: {
              clientId: 'sms',
              ...smsServiceOptions,
            },
            consumer: {
              groupId: 'sms-consumer',
            },
          },
        });
      },
      inject: [ConfigService],
    },
    GsmService,
  ],
})
export class GsmModule {}
