import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { KafkaOptions, Transport } from '@nestjs/microservices';
import Config from './config/configuration';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<KafkaOptions>(AppModule, {
    transport: Transport.KAFKA,
    options: {
      client: {
        clientId: 'gsm',
        ...Config().transport,
      },
      consumer: {
        groupId: 'gsm-consumer',
      },
    },
  });
  await app.listen();
}
bootstrap();
