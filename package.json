{"name": "@sms-receiver/gsm-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^8.0.1", "@nestjs/config": "^1.0.0", "@nestjs/core": "^8.0.1", "@nestjs/microservices": "^8.0.1", "@nestjs/platform-express": "^8.0.1", "@nestjs/schedule": "^1.0.0", "@sms-receiver/common": "^0.0.2", "@sms-receiver/dto": "^0.0.6", "kafkajs": "^1.15.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "serialport": "^9.2.0", "serialport-gsm": "^4.0.1"}, "devDependencies": {"@nestjs/cli": "^8.0.1", "@nestjs/schematics": "^7.3.0", "@nestjs/testing": "^8.0.1", "@types/cron": "^1.7.3", "@types/express": "^4.17.11", "@types/jest": "^26.0.22", "@types/node": "^14.14.36", "@types/supertest": "^2.0.10", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "eslint": "^7.22.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "jest": "^26.6.3", "prettier": "^2.2.1", "supertest": "^6.1.3", "ts-jest": "^26.5.4", "ts-loader": "^8.0.18", "ts-node": "^9.1.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.2.3", "webpack": "^5.44.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"]}}