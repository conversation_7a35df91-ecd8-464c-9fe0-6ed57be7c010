[package]
name = "sms-worker-rust"
version = "0.1.0"
edition = "2021"
description = "Rust implementation of SMS receiver service for GSM modems"
authors = ["SMS Receiver Team"]

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tokio-serial = "5.4"
rdkafka = { version = "0.36", features = ["cmake-build"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
config = "0.14"
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"
regex = "1.0"
futures = "0.3"
async-trait = "0.1"
clap = { version = "4.0", features = ["derive"] }

[dev-dependencies]
tokio-test = "0.4"
